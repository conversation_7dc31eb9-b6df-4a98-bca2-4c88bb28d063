# MCP交互式工具使用指南

## 🎯 项目概述

本项目实现了一个基于MCP (Model Context Protocol) 协议的交互式工具，能够在AI模型执行过程中暂停并等待用户的实时输入。该工具使用SSE (Server-Sent Events) 协议实现高效的双向通信。

## ✨ 核心特性

- **🔄 交互式暂停**: AI可以通过MCP协议调用工具暂停执行，等待用户输入
- **⚡ 实时输入捕获**: 支持文本、数字、JSON、多行文本等多种输入类型
- **⏰ 超时控制**: 可配置的超时机制，防止无限等待
- **🌐 SSE协议**: 基于Server-Sent Events的高效通信
- **🔧 MCP兼容**: 完全符合MCP协议规范

## 🚀 快速开始

### 1. 环境要求

- Python 3.10+
- uv 包管理器（推荐）或 pip

### 2. 安装依赖

```bash
# 使用uv（推荐）
uv sync

# 或使用pip
pip install -r requirements.txt
```

### 3. 启动服务器

```bash
# 基本启动
uv run python main.py

# 或使用启动脚本（更多选项）
uv run python start_server.py

# 开发模式（自动重载）
uv run python start_server.py --reload --debug
```

### 4. 验证安装

```bash
# 运行简单测试
uv run python simple_test.py

# 检查服务器健康状态
curl http://localhost:7000/health
```

## 🔧 工具说明

### wait_for_user_input

**功能**: 暂停执行并等待用户输入

**参数**:
- `prompt` (string): 显示给用户的提示信息，默认"请输入内容: "
- `timeout` (integer): 等待超时时间（秒），默认300秒
- `input_type` (string): 输入类型，支持：
  - `text`: 普通文本（默认）
  - `number`: 数字
  - `json`: JSON格式
  - `multiline`: 多行文本

**使用示例**:
```python
# 基本文本输入
result = await mcp_client.call_tool("wait_for_user_input", {
    "prompt": "请输入您的姓名: ",
    "timeout": 60,
    "input_type": "text"
})

# JSON输入
result = await mcp_client.call_tool("wait_for_user_input", {
    "prompt": "请输入配置JSON: ",
    "timeout": 120,
    "input_type": "json"
})
```

### check_input_status

**功能**: 检查当前是否正在等待用户输入

**参数**: 无

**返回**: 当前输入状态信息

## 🌐 API端点

- `GET /health` - 健康检查
- `GET /info` - 服务器信息
- `GET /sse` - SSE连接端点
- `POST /messages/` - 消息处理端点

## 🧪 测试和演示

### 1. 运行测试客户端

```bash
uv run python test_client.py
```

选择测试模式：
1. 完整演示（包含用户输入）
2. 超时测试

### 2. 运行完整演示

```bash
uv run python demo.py
```

演示场景：
1. AI对话场景（收集信息、配置偏好）
2. 交互式调试场景
3. 渐进式优化场景

### 3. 使用Makefile

```bash
# 查看所有可用命令
make help

# 安装依赖
make install

# 启动服务器
make start

# 运行测试
make test

# 运行演示
make demo
```

## ⚙️ 配置选项

### 环境变量

创建 `.env` 文件或设置环境变量：

```bash
# 服务器配置
MCP_HOST=0.0.0.0
MCP_PORT=7000
MCP_LOG_LEVEL=INFO

# 用户输入配置
MCP_INPUT_TIMEOUT=300
MCP_MAX_INPUT_TIMEOUT=3600

# CORS配置
MCP_CORS_ORIGINS=*
```

### 生成配置文件

```bash
uv run python config.py
```

## 🔍 实现原理

### 1. MCP协议集成

- 使用FastMCP库简化MCP服务器开发
- 通过装饰器定义工具函数
- 标准JSON-RPC 2.0消息格式

### 2. SSE协议实现

- 基于Starlette的SSE传输层
- 支持长连接和实时事件推送
- 自动处理连接管理

### 3. 用户输入处理

- 线程安全的输入队列
- 阻塞式输入捕获
- 超时和错误处理

## 🚨 注意事项

### 1. 部署环境

- **开发环境**: 支持控制台输入
- **生产环境**: 需要考虑输入来源
- **容器环境**: 确保TTY支持

### 2. 安全考虑

- 输入验证和清理
- 超时控制防止资源耗尽
- CORS配置限制访问

### 3. 性能优化

- 合理设置超时时间
- 监控并发连接数
- 定期清理过期会话

## 🔧 故障排除

### 常见问题

1. **服务器启动失败**
   ```bash
   # 检查端口占用
   lsof -i :7000
   
   # 更换端口
   MCP_PORT=8001 uv run python main.py
   ```

2. **依赖安装问题**
   ```bash
   # 重新安装依赖
   uv sync --reinstall
   
   # 或使用pip
   pip install -r requirements.txt --force-reinstall
   ```

3. **输入超时**
   ```bash
   # 增加超时时间
   MCP_INPUT_TIMEOUT=600 uv run python main.py
   ```

### 调试模式

```bash
# 启用详细日志
uv run python start_server.py --debug --log-level DEBUG
```

## 📚 扩展开发

### 添加新工具

使用FastMCP装饰器：

```python
@mcp.tool()
async def my_new_tool(param1: str, param2: int = 10) -> str:
    """新工具的描述"""
    # 工具逻辑
    return "结果"
```

### 自定义输入处理

继承UserInputCapture类：

```python
class CustomInputCapture(UserInputCapture):
    def wait_for_input(self, prompt: str, timeout: int) -> str:
        # 自定义输入逻辑
        pass
```

## 📞 支持

如有问题或建议，请：
1. 查看README.md获取详细文档
2. 运行测试脚本诊断问题
3. 检查日志输出获取错误信息

---

**版本**: 1.0.0  
**更新时间**: 2025-06-16
