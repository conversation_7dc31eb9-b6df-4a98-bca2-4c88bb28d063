#!/usr/bin/env python3
"""
MCP交互式工具测试客户端
演示如何通过SSE协议与交互式MCP服务器通信
"""

import asyncio
import json
import logging
import time
from typing import Any, Dict, Optional

import httpx
from httpx_sse import aconnect_sse

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class MCPClient:
    """MCP客户端，通过SSE协议与服务器通信"""
    
    def __init__(self, base_url: str = "http://localhost:7000"):
        self.base_url = base_url
        self.sse_url = f"{base_url}/sse/"
        self.client = httpx.AsyncClient(timeout=30.0)
        self.request_id = 0
    
    def _get_next_id(self) -> int:
        """获取下一个请求ID"""
        self.request_id += 1
        return self.request_id
    
    async def check_server_health(self) -> bool:
        """检查服务器健康状态"""
        try:
            response = await self.client.get(f"{self.base_url}/health")
            if response.status_code == 200:
                health_data = response.json()
                logger.info(f"服务器健康状态: {health_data}")
                return True
            return False
        except Exception as e:
            logger.error(f"检查服务器健康状态失败: {e}")
            return False
    
    async def get_server_info(self) -> Optional[Dict[str, Any]]:
        """获取服务器信息"""
        try:
            response = await self.client.get(f"{self.base_url}/info")
            if response.status_code == 200:
                return response.json()
            return None
        except Exception as e:
            logger.error(f"获取服务器信息失败: {e}")
            return None
    
    async def list_tools(self) -> Optional[Dict[str, Any]]:
        """列出可用工具"""
        request_data = {
            "jsonrpc": "2.0",
            "id": self._get_next_id(),
            "method": "tools/list",
            "params": {}
        }
        
        return await self._send_request(request_data)
    
    async def call_tool(self, tool_name: str, arguments: Dict[str, Any] = None) -> Optional[Dict[str, Any]]:
        """调用工具"""
        request_data = {
            "jsonrpc": "2.0",
            "id": self._get_next_id(),
            "method": "tools/call",
            "params": {
                "name": tool_name,
                "arguments": arguments or {}
            }
        }
        
        return await self._send_request(request_data)
    
    async def _send_request(self, request_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """发送请求并等待响应"""
        try:
            logger.info(f"发送请求: {json.dumps(request_data, ensure_ascii=False, indent=2)}")
            
            async with aconnect_sse(
                self.client,
                "GET",
                self.sse_url,
                json=request_data,
                headers={"Accept": "text/event-stream"}
            ) as event_source:
                async for sse_event in event_source.aiter_sse():
                    if sse_event.event == "message":
                        try:
                            response_data = json.loads(sse_event.data)
                            logger.info(f"收到响应: {json.dumps(response_data, ensure_ascii=False, indent=2)}")
                            return response_data
                        except json.JSONDecodeError as e:
                            logger.error(f"解析响应JSON失败: {e}")
                            logger.error(f"原始数据: {sse_event.data}")
                    elif sse_event.event == "error":
                        logger.error(f"服务器错误: {sse_event.data}")
                        return None
                        
        except Exception as e:
            logger.error(f"发送请求失败: {e}")
            return None
    
    async def close(self):
        """关闭客户端"""
        await self.client.aclose()


async def demo_interactive_tool():
    """演示交互式工具的使用"""
    client = MCPClient()
    
    try:
        print("🔍 检查服务器状态...")
        if not await client.check_server_health():
            print("❌ 服务器不可用，请确保服务器正在运行")
            return
        
        print("✅ 服务器运行正常")
        
        # 获取服务器信息
        print("\n📋 获取服务器信息...")
        server_info = await client.get_server_info()
        if server_info:
            print(f"服务器信息: {json.dumps(server_info, ensure_ascii=False, indent=2)}")
        
        # 列出可用工具
        print("\n🔧 列出可用工具...")
        tools_response = await client.list_tools()
        if tools_response and "result" in tools_response:
            tools = tools_response["result"]["tools"]
            print(f"可用工具数量: {len(tools)}")
            for tool in tools:
                print(f"  - {tool['name']}: {tool['description']}")
        
        # 检查输入状态
        print("\n📊 检查输入状态...")
        status_response = await client.call_tool("check_input_status")
        if status_response:
            print(f"状态检查结果: {status_response}")
        
        # 演示用户输入捕获
        print("\n🎯 演示用户输入捕获...")
        print("注意: 这个调用会阻塞，直到用户在服务器端输入内容")
        
        # 调用交互式工具
        input_response = await client.call_tool(
            "wait_for_user_input",
            {
                "prompt": "请输入您的姓名: ",
                "timeout": 60,
                "input_type": "text"
            }
        )
        
        if input_response:
            print(f"用户输入结果: {json.dumps(input_response, ensure_ascii=False, indent=2)}")
        
        # 演示JSON输入
        print("\n📝 演示JSON输入...")
        json_response = await client.call_tool(
            "wait_for_user_input",
            {
                "prompt": "请输入一个JSON对象 (例如: {\"name\": \"张三\", \"age\": 25}): ",
                "timeout": 120,
                "input_type": "json"
            }
        )
        
        if json_response:
            print(f"JSON输入结果: {json.dumps(json_response, ensure_ascii=False, indent=2)}")
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
    except Exception as e:
        logger.error(f"演示过程中发生错误: {e}")
    finally:
        await client.close()


async def test_timeout_scenario():
    """测试超时场景"""
    client = MCPClient()
    
    try:
        print("⏰ 测试超时场景...")
        print("这个测试会在5秒后超时")
        
        timeout_response = await client.call_tool(
            "wait_for_user_input",
            {
                "prompt": "这是一个超时测试，请不要输入任何内容: ",
                "timeout": 5,
                "input_type": "text"
            }
        )
        
        if timeout_response:
            print(f"超时测试结果: {json.dumps(timeout_response, ensure_ascii=False, indent=2)}")
        
    except Exception as e:
        logger.error(f"超时测试失败: {e}")
    finally:
        await client.close()


def main():
    """主函数"""
    print("🚀 启动MCP交互式工具测试客户端")
    print("📡 连接到服务器: http://localhost:7000")
    print("=" * 60)
    
    # 选择测试模式
    print("\n请选择测试模式:")
    print("1. 完整演示 (包含用户输入)")
    print("2. 超时测试")
    print("3. 退出")
    
    try:
        choice = input("请输入选择 (1-3): ").strip()
        
        if choice == "1":
            asyncio.run(demo_interactive_tool())
        elif choice == "2":
            asyncio.run(test_timeout_scenario())
        elif choice == "3":
            print("👋 再见!")
        else:
            print("❌ 无效选择")
    
    except KeyboardInterrupt:
        print("\n👋 再见!")
    except Exception as e:
        logger.error(f"程序执行失败: {e}")


if __name__ == "__main__":
    main()
