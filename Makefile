# MCP交互式工具 Makefile

.PHONY: help install dev start test demo clean lint format check health

# 默认目标
help:
	@echo "🔧 MCP交互式工具 - 可用命令:"
	@echo ""
	@echo "📦 安装和设置:"
	@echo "  make install     - 安装依赖包"
	@echo "  make dev         - 安装开发依赖"
	@echo ""
	@echo "🚀 运行服务:"
	@echo "  make start       - 启动MCP服务器"
	@echo "  make start-dev   - 启动开发模式服务器 (自动重载)"
	@echo "  make start-debug - 启动调试模式服务器"
	@echo ""
	@echo "🧪 测试和演示:"
	@echo "  make test        - 运行测试客户端"
	@echo "  make demo        - 运行完整演示"
	@echo "  make health      - 检查服务器健康状态"
	@echo ""
	@echo "🔍 代码质量:"
	@echo "  make lint        - 运行代码检查"
	@echo "  make format      - 格式化代码"
	@echo "  make check       - 运行所有检查"
	@echo ""
	@echo "🧹 清理:"
	@echo "  make clean       - 清理临时文件"
	@echo "  make clean-all   - 清理所有生成文件"

# 安装依赖
install:
	@echo "📦 安装依赖包..."
	@if command -v uv >/dev/null 2>&1; then \
		echo "使用 uv 安装依赖..."; \
		uv sync; \
	else \
		echo "使用 pip 安装依赖..."; \
		pip install -r requirements.txt; \
	fi
	@echo "✅ 依赖安装完成"

# 安装开发依赖
dev: install
	@echo "📦 安装开发依赖..."
	@pip install black flake8 mypy pytest pytest-asyncio
	@echo "✅ 开发环境设置完成"

# 启动服务器
start:
	@echo "🚀 启动MCP服务器..."
	@python start_server.py

# 启动开发模式服务器
start-dev:
	@echo "🚀 启动开发模式服务器 (自动重载)..."
	@python start_server.py --reload --debug

# 启动调试模式服务器
start-debug:
	@echo "🚀 启动调试模式服务器..."
	@python start_server.py --debug --log-level DEBUG

# 运行测试客户端
test:
	@echo "🧪 运行测试客户端..."
	@python test_client.py

# 运行完整演示
demo:
	@echo "🎭 运行完整演示..."
	@python demo.py

# 检查服务器健康状态
health:
	@echo "🔍 检查服务器健康状态..."
	@curl -s http://localhost:7000/health | python -m json.tool || echo "❌ 服务器未运行或不可访问"

# 显示服务器信息
info:
	@echo "📋 获取服务器信息..."
	@curl -s http://localhost:7000/info | python -m json.tool || echo "❌ 服务器未运行或不可访问"

# 代码检查
lint:
	@echo "🔍 运行代码检查..."
	@echo "检查 Python 语法..."
	@python -m py_compile main.py test_client.py config.py start_server.py demo.py
	@if command -v flake8 >/dev/null 2>&1; then \
		echo "运行 flake8..."; \
		flake8 --max-line-length=88 --ignore=E203,W503 *.py; \
	else \
		echo "⚠️ flake8 未安装，跳过检查"; \
	fi

# 格式化代码
format:
	@echo "🎨 格式化代码..."
	@if command -v black >/dev/null 2>&1; then \
		black --line-length=80 *.py; \
		echo "✅ 代码格式化完成"; \
	else \
		echo "⚠️ black 未安装，请运行 'make dev' 安装开发依赖"; \
	fi

# 类型检查
typecheck:
	@echo "🔍 运行类型检查..."
	@if command -v mypy >/dev/null 2>&1; then \
		mypy --ignore-missing-imports *.py; \
	else \
		echo "⚠️ mypy 未安装，跳过类型检查"; \
	fi

# 运行所有检查
check: lint typecheck
	@echo "✅ 所有检查完成"

# 清理临时文件
clean:
	@echo "🧹 清理临时文件..."
	@find . -type f -name "*.pyc" -delete
	@find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
	@find . -type d -name "*.egg-info" -exec rm -rf {} + 2>/dev/null || true
	@rm -f .coverage
	@echo "✅ 清理完成"

# 清理所有生成文件
clean-all: clean
	@echo "🧹 清理所有生成文件..."
	@rm -f .env
	@rm -rf dist/
	@rm -rf build/
	@echo "✅ 深度清理完成"

# 创建配置文件
config:
	@echo "⚙️ 创建配置文件..."
	@python config.py

# 显示配置信息
show-config:
	@echo "📋 显示当前配置..."
	@python -c "from config import print_config_info; print_config_info()"

# 运行性能测试
benchmark:
	@echo "⚡ 运行性能测试..."
	@echo "测试并发连接..."
	@for i in {1..5}; do \
		curl -s http://localhost:7000/health >/dev/null & \
	done; \
	wait; \
	echo "✅ 并发测试完成"

# 生成文档
docs:
	@echo "📚 生成文档..."
	@echo "当前文档文件:"
	@ls -la *.md
	@echo "README.md 已包含完整文档"

# 打包项目
package:
	@echo "📦 打包项目..."
	@python -m build 2>/dev/null || echo "⚠️ build 模块未安装，使用基本打包"
	@tar -czf mcp-interactive-tool.tar.gz \
		*.py *.md *.txt Makefile \
		--exclude="__pycache__" \
		--exclude="*.pyc" \
		--exclude=".env"
	@echo "✅ 项目已打包为 mcp-interactive-tool.tar.gz"

# 快速启动 (一键启动)
quick-start: install start

# 完整测试流程
full-test: install
	@echo "🔄 运行完整测试流程..."
	@echo "1. 启动服务器 (后台)..."
	@python start_server.py &
	@SERVER_PID=$$!; \
	sleep 3; \
	echo "2. 检查服务器状态..."; \
	make health; \
	echo "3. 运行测试客户端..."; \
	python test_client.py; \
	echo "4. 停止服务器..."; \
	kill $$SERVER_PID 2>/dev/null || true
	@echo "✅ 完整测试流程完成"

# 开发环境设置
setup-dev: dev
	@echo "🔧 设置开发环境..."
	@make config
	@echo "✅ 开发环境设置完成"
	@echo ""
	@echo "下一步:"
	@echo "  1. 运行 'make start-dev' 启动开发服务器"
	@echo "  2. 在另一个终端运行 'make test' 测试功能"
	@echo "  3. 运行 'make demo' 查看完整演示"
