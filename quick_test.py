#!/usr/bin/env python3
"""
快速测试脚本 - 验证MCP交互式工具的基本功能
"""

import asyncio
import json
import subprocess
import sys
import time
from pathlib import Path

def check_dependencies():
    """检查基本依赖"""
    required_modules = ['asyncio', 'json', 'threading', 'queue']
    missing = []
    
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing.append(module)
    
    if missing:
        print(f"❌ 缺少基本模块: {missing}")
        return False
    
    print("✅ 基本依赖检查通过")
    return True

def test_imports():
    """测试导入"""
    print("🔍 测试模块导入...")
    
    try:
        # 测试主要模块导入
        import main
        print("✅ main.py 导入成功")
        
        import config
        print("✅ config.py 导入成功")
        
        import test_client
        print("✅ test_client.py 导入成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        print("请确保已安装所需依赖: pip install -r requirements.txt")
        return False

def test_config():
    """测试配置"""
    print("⚙️ 测试配置...")
    
    try:
        from config import MCPServerConfig, MCPClientConfig
        
        server_config = MCPServerConfig.to_dict()
        client_config = MCPClientConfig.to_dict()
        
        print(f"  服务器配置: {len(server_config)} 项")
        print(f"  客户端配置: {len(client_config)} 项")
        print("✅ 配置测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

def test_user_input_capture():
    """测试用户输入捕获类"""
    print("📝 测试用户输入捕获...")
    
    try:
        from main import UserInputCapture
        
        capture = UserInputCapture()
        
        # 测试基本属性
        assert hasattr(capture, 'input_queue')
        assert hasattr(capture, 'is_waiting')
        assert hasattr(capture, 'current_prompt')
        assert hasattr(capture, 'timeout_seconds')
        
        # 测试方法
        assert hasattr(capture, 'wait_for_input')
        assert hasattr(capture, '_input_worker')
        
        print("✅ 用户输入捕获类测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 用户输入捕获测试失败: {e}")
        return False

def test_mcp_server():
    """测试MCP服务器类"""
    print("🖥️ 测试MCP服务器...")
    
    try:
        from main import InteractiveMCPServer
        
        server = InteractiveMCPServer()
        
        # 测试基本属性
        assert hasattr(server, 'server')
        assert hasattr(server, 'setup_tools')
        
        print("✅ MCP服务器类测试通过")
        return True
        
    except Exception as e:
        print(f"❌ MCP服务器测试失败: {e}")
        return False

def test_app_creation():
    """测试应用创建"""
    print("🚀 测试应用创建...")
    
    try:
        from main import create_app
        
        app = create_app()
        
        # 检查应用类型
        from starlette.applications import Starlette
        assert isinstance(app, Starlette)
        
        print("✅ 应用创建测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 应用创建测试失败: {e}")
        return False

async def test_client_creation():
    """测试客户端创建"""
    print("📱 测试客户端创建...")
    
    try:
        from test_client import MCPClient
        
        client = MCPClient()
        
        # 测试基本属性
        assert hasattr(client, 'base_url')
        assert hasattr(client, 'sse_url')
        assert hasattr(client, 'client')
        
        # 测试方法
        assert hasattr(client, 'check_server_health')
        assert hasattr(client, 'list_tools')
        assert hasattr(client, 'call_tool')
        
        await client.close()
        
        print("✅ 客户端创建测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 客户端创建测试失败: {e}")
        return False

def test_file_structure():
    """测试文件结构"""
    print("📁 测试文件结构...")
    
    required_files = [
        'main.py',
        'test_client.py', 
        'config.py',
        'start_server.py',
        'demo.py',
        'requirements.txt',
        'README.md',
        'Makefile'
    ]
    
    missing_files = []
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ 缺少文件: {missing_files}")
        return False
    
    print(f"✅ 文件结构完整 ({len(required_files)} 个文件)")
    return True

def main():
    """主测试函数"""
    print("🧪 MCP交互式工具 - 快速测试")
    print("=" * 50)
    
    tests = [
        ("基本依赖检查", check_dependencies),
        ("文件结构检查", test_file_structure),
        ("模块导入测试", test_imports),
        ("配置测试", test_config),
        ("用户输入捕获测试", test_user_input_capture),
        ("MCP服务器测试", test_mcp_server),
        ("应用创建测试", test_app_creation),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}...")
        try:
            if test_func():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            failed += 1
    
    # 异步测试
    print(f"\n🔍 客户端创建测试...")
    try:
        if asyncio.run(test_client_creation()):
            passed += 1
        else:
            failed += 1
    except Exception as e:
        print(f"❌ 客户端创建测试异常: {e}")
        failed += 1
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 测试结果总结")
    print("=" * 50)
    print(f"✅ 通过: {passed}")
    print(f"❌ 失败: {failed}")
    print(f"📈 成功率: {passed/(passed+failed)*100:.1f}%")
    
    if failed == 0:
        print("\n🎉 所有测试通过！系统准备就绪。")
        print("\n下一步:")
        print("  1. 运行 'python start_server.py' 启动服务器")
        print("  2. 在另一个终端运行 'python test_client.py' 测试功能")
        print("  3. 运行 'python demo.py' 查看完整演示")
        return True
    else:
        print(f"\n⚠️ 有 {failed} 个测试失败，请检查并修复问题。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
