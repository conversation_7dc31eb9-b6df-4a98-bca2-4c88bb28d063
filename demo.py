#!/usr/bin/env python3
"""
MCP交互式工具完整演示脚本
展示如何在实际AI应用中使用交互式用户输入功能
"""

import asyncio
import json
import logging
import time
from typing import Dict, Any, List

from test_client import MCPClient

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class AIAssistantDemo:
    """AI助手演示类，模拟AI模型使用交互式工具的场景"""
    
    def __init__(self, server_url: str = "http://localhost:7000"):
        self.client = MCPClient(server_url)
        self.conversation_history: List[Dict[str, Any]] = []
    
    async def initialize(self) -> bool:
        """初始化连接"""
        print("🔄 初始化AI助手...")
        
        # 检查服务器状态
        if not await self.client.check_server_health():
            print("❌ 无法连接到MCP服务器")
            return False
        
        # 获取可用工具
        tools_response = await self.client.list_tools()
        if not tools_response:
            print("❌ 无法获取工具列表")
            return False
        
        tools = tools_response.get("result", {}).get("tools", [])
        print(f"✅ 已连接到MCP服务器，可用工具: {len(tools)}个")
        
        return True
    
    async def simulate_ai_conversation(self):
        """模拟AI对话场景"""
        print("\n" + "="*60)
        print("🤖 AI助手对话演示")
        print("="*60)
        
        # 场景1: 收集用户基本信息
        print("\n📋 场景1: 收集用户基本信息")
        print("AI: 你好！我是AI助手，让我来了解一下你的基本信息。")
        
        # 获取用户姓名
        name_response = await self.client.call_tool(
            "wait_for_user_input",
            {
                "prompt": "请输入您的姓名: ",
                "timeout": 60,
                "input_type": "text"
            }
        )
        
        if name_response and not name_response.get("isError"):
            user_name = self._extract_user_input(name_response)
            print(f"AI: 很高兴认识你，{user_name}！")
            self.conversation_history.append({
                "type": "user_info",
                "field": "name",
                "value": user_name
            })
        else:
            print("AI: 抱歉，没有收到您的姓名。")
            return
        
        # 获取用户年龄
        age_response = await self.client.call_tool(
            "wait_for_user_input",
            {
                "prompt": "请输入您的年龄: ",
                "timeout": 30,
                "input_type": "number"
            }
        )
        
        if age_response and not age_response.get("isError"):
            user_age = self._extract_user_input(age_response)
            print(f"AI: 了解了，您今年{user_age}岁。")
            self.conversation_history.append({
                "type": "user_info",
                "field": "age", 
                "value": user_age
            })
        
        # 场景2: 配置偏好设置
        print(f"\n⚙️ 场景2: 配置偏好设置")
        print(f"AI: {user_name}，现在让我们配置一下您的偏好设置。")
        
        preferences_response = await self.client.call_tool(
            "wait_for_user_input",
            {
                "prompt": "请输入您的偏好设置 (JSON格式，例如: {\"language\": \"zh\", \"theme\": \"dark\"}): ",
                "timeout": 120,
                "input_type": "json"
            }
        )
        
        if preferences_response and not preferences_response.get("isError"):
            preferences = self._extract_user_input(preferences_response)
            print(f"AI: 好的，我已经记录了您的偏好设置。")
            try:
                prefs_dict = json.loads(preferences)
                for key, value in prefs_dict.items():
                    print(f"  - {key}: {value}")
                self.conversation_history.append({
                    "type": "preferences",
                    "value": prefs_dict
                })
            except json.JSONDecodeError:
                print("AI: 偏好设置格式有误，将使用默认设置。")
        
        # 场景3: 多行文本输入
        print(f"\n📝 场景3: 多行文本输入")
        print(f"AI: 最后，请告诉我您希望我帮助您完成什么任务？")
        
        task_response = await self.client.call_tool(
            "wait_for_user_input",
            {
                "prompt": "请详细描述您的任务需求 (支持多行输入): ",
                "timeout": 180,
                "input_type": "multiline"
            }
        )
        
        if task_response and not task_response.get("isError"):
            task_description = self._extract_user_input(task_response)
            print(f"AI: 我明白了您的需求：")
            print(f"「{task_description}」")
            self.conversation_history.append({
                "type": "task",
                "value": task_description
            })
        
        # 总结对话
        await self.summarize_conversation()
    
    async def simulate_interactive_debugging(self):
        """模拟交互式调试场景"""
        print("\n" + "="*60)
        print("🐛 交互式调试演示")
        print("="*60)
        
        print("AI: 我在处理您的请求时遇到了一个问题，需要您的帮助来调试。")
        
        # 模拟错误场景
        error_info = {
            "error_type": "ValidationError",
            "message": "输入数据格式不正确",
            "suggestions": [
                "检查JSON格式是否正确",
                "确认所有必需字段都已提供",
                "验证数据类型是否匹配"
            ]
        }
        
        print(f"AI: 错误详情：")
        print(f"  类型: {error_info['error_type']}")
        print(f"  消息: {error_info['message']}")
        print(f"  建议:")
        for i, suggestion in enumerate(error_info['suggestions'], 1):
            print(f"    {i}. {suggestion}")
        
        # 请求用户选择调试选项
        debug_response = await self.client.call_tool(
            "wait_for_user_input",
            {
                "prompt": "请选择调试选项 (1-3) 或输入 'skip' 跳过: ",
                "timeout": 60,
                "input_type": "text"
            }
        )
        
        if debug_response and not debug_response.get("isError"):
            user_choice = self._extract_user_input(debug_response).strip()
            
            if user_choice in ["1", "2", "3"]:
                suggestion = error_info['suggestions'][int(user_choice) - 1]
                print(f"AI: 好的，我将按照建议「{suggestion}」来处理。")
                
                # 请求修正后的输入
                fix_response = await self.client.call_tool(
                    "wait_for_user_input",
                    {
                        "prompt": "请提供修正后的输入数据: ",
                        "timeout": 120,
                        "input_type": "json"
                    }
                )
                
                if fix_response and not fix_response.get("isError"):
                    fixed_data = self._extract_user_input(fix_response)
                    print(f"AI: 谢谢！我已经收到修正后的数据，继续处理...")
                    print(f"修正数据: {fixed_data}")
            
            elif user_choice.lower() == "skip":
                print("AI: 好的，我将跳过这个步骤并使用默认处理方式。")
            
            else:
                print("AI: 无效的选择，将使用默认处理方式。")
    
    async def simulate_progressive_refinement(self):
        """模拟渐进式优化场景"""
        print("\n" + "="*60)
        print("🎯 渐进式优化演示")
        print("="*60)
        
        print("AI: 我将为您生成一个解决方案，并根据您的反馈进行优化。")
        
        # 初始方案
        initial_solution = {
            "approach": "基础实现",
            "features": ["核心功能", "基本界面"],
            "estimated_time": "2小时"
        }
        
        print(f"\n📋 初始方案:")
        print(f"  方法: {initial_solution['approach']}")
        print(f"  功能: {', '.join(initial_solution['features'])}")
        print(f"  预计时间: {initial_solution['estimated_time']}")
        
        # 获取用户反馈
        feedback_response = await self.client.call_tool(
            "wait_for_user_input",
            {
                "prompt": "请对这个方案提供反馈 (满意/需要改进/重新设计): ",
                "timeout": 90,
                "input_type": "text"
            }
        )
        
        if feedback_response and not feedback_response.get("isError"):
            feedback = self._extract_user_input(feedback_response).strip().lower()
            
            if "满意" in feedback:
                print("AI: 太好了！我将按照这个方案开始实施。")
            
            elif "改进" in feedback:
                print("AI: 我来优化一下方案...")
                
                # 请求具体改进建议
                improvement_response = await self.client.call_tool(
                    "wait_for_user_input",
                    {
                        "prompt": "请具体说明需要改进的地方: ",
                        "timeout": 120,
                        "input_type": "multiline"
                    }
                )
                
                if improvement_response and not improvement_response.get("isError"):
                    improvements = self._extract_user_input(improvement_response)
                    print(f"AI: 根据您的建议「{improvements}」，我已经优化了方案。")
                    
                    # 展示优化后的方案
                    optimized_solution = {
                        "approach": "优化实现",
                        "features": ["核心功能", "高级界面", "性能优化", "用户体验增强"],
                        "estimated_time": "3小时"
                    }
                    
                    print(f"\n📋 优化方案:")
                    print(f"  方法: {optimized_solution['approach']}")
                    print(f"  功能: {', '.join(optimized_solution['features'])}")
                    print(f"  预计时间: {optimized_solution['estimated_time']}")
            
            elif "重新设计" in feedback:
                print("AI: 我将重新设计方案...")
                
                # 请求新的需求
                new_requirements_response = await self.client.call_tool(
                    "wait_for_user_input",
                    {
                        "prompt": "请描述您期望的新方案要求: ",
                        "timeout": 150,
                        "input_type": "multiline"
                    }
                )
                
                if new_requirements_response and not new_requirements_response.get("isError"):
                    new_requirements = self._extract_user_input(new_requirements_response)
                    print(f"AI: 基于新需求「{new_requirements}」，我设计了全新的方案。")
    
    async def summarize_conversation(self):
        """总结对话内容"""
        print("\n" + "="*60)
        print("📊 对话总结")
        print("="*60)
        
        print("AI: 让我总结一下我们的对话内容：")
        
        for i, item in enumerate(self.conversation_history, 1):
            if item["type"] == "user_info":
                print(f"  {i}. 用户{item['field']}: {item['value']}")
            elif item["type"] == "preferences":
                print(f"  {i}. 偏好设置: {json.dumps(item['value'], ensure_ascii=False)}")
            elif item["type"] == "task":
                print(f"  {i}. 任务需求: {item['value'][:50]}...")
        
        # 询问是否需要保存
        save_response = await self.client.call_tool(
            "wait_for_user_input",
            {
                "prompt": "是否需要保存这次对话记录？(y/n): ",
                "timeout": 30,
                "input_type": "text"
            }
        )
        
        if save_response and not save_response.get("isError"):
            save_choice = self._extract_user_input(save_response).strip().lower()
            if save_choice in ["y", "yes", "是", "需要"]:
                print("AI: 好的，对话记录已保存。")
            else:
                print("AI: 好的，不保存对话记录。")
    
    def _extract_user_input(self, response: Dict[str, Any]) -> str:
        """从响应中提取用户输入"""
        if response and "result" in response:
            content = response["result"].get("content", [])
            for item in content:
                if item.get("type") == "text":
                    text = item.get("text", "")
                    # 尝试从详细信息中提取用户输入
                    if "详细信息:" in text:
                        try:
                            detail_start = text.find("详细信息:") + len("详细信息:")
                            detail_json = text[detail_start:].strip()
                            detail_data = json.loads(detail_json)
                            return detail_data.get("user_input", "")
                        except:
                            pass
                    # 从主要内容中提取
                    if "输入内容:" in text:
                        lines = text.split("\n")
                        for line in lines:
                            if line.startswith("输入内容:"):
                                return line.replace("输入内容:", "").strip()
        return ""
    
    async def close(self):
        """关闭连接"""
        await self.client.close()


async def main():
    """主演示函数"""
    print("🎭 MCP交互式工具完整演示")
    print("=" * 60)
    print("这个演示将展示AI如何使用交互式工具与用户进行实时交互")
    print("请确保MCP服务器正在运行 (python main.py)")
    print("=" * 60)
    
    demo = AIAssistantDemo()
    
    try:
        # 初始化
        if not await demo.initialize():
            return
        
        # 选择演示场景
        print("\n请选择演示场景:")
        print("1. AI对话场景 (收集信息、配置偏好)")
        print("2. 交互式调试场景")
        print("3. 渐进式优化场景")
        print("4. 运行所有场景")
        
        choice = input("\n请输入选择 (1-4): ").strip()
        
        if choice == "1":
            await demo.simulate_ai_conversation()
        elif choice == "2":
            await demo.simulate_interactive_debugging()
        elif choice == "3":
            await demo.simulate_progressive_refinement()
        elif choice == "4":
            await demo.simulate_ai_conversation()
            await demo.simulate_interactive_debugging()
            await demo.simulate_progressive_refinement()
        else:
            print("❌ 无效选择")
            return
        
        print("\n🎉 演示完成！")
        print("这展示了AI如何通过MCP协议实现与用户的实时交互。")
        
    except KeyboardInterrupt:
        print("\n⚠️ 演示被用户中断")
    except Exception as e:
        logger.error(f"演示过程中发生错误: {e}")
    finally:
        await demo.close()


if __name__ == "__main__":
    asyncio.run(main())
