# MCP交互式工具 - 基于SSE协议的用户输入捕获系统

## 📖 项目简介

这是一个基于MCP (Model Context Protocol) 协议的交互式工具，专门设计用于在AI模型执行过程中暂停并等待用户的实时输入。该工具严格遵循MCP协议规范，使用SSE (Server-Sent Events) 协议实现高效的双向通信。

## ✨ 核心功能

- **🔄 交互式暂停**: AI通过MCP协议调用工具时，工具会暂停执行并等待用户输入
- **⚡ 实时输入捕获**: 支持多种输入类型（文本、数字、JSON、多行文本）
- **⏰ 超时控制**: 可配置的超时机制，防止无限等待
- **🌐 SSE协议**: 基于Server-Sent Events实现的高效通信
- **🔧 MCP兼容**: 完全符合MCP协议规范
- **📊 状态监控**: 实时监控输入状态和服务器健康状况

## 🏗️ 技术架构

### 核心组件

1. **MCP服务器** (`main.py`)
   - 基于`mcp`库实现的标准MCP服务器
   - 使用Starlette框架提供HTTP/SSE服务
   - 支持CORS跨域访问

2. **用户输入捕获器** (`UserInputCapture`)
   - 线程安全的输入队列管理
   - 阻塞式用户输入处理
   - 超时和错误处理机制

3. **测试客户端** (`test_client.py`)
   - 演示如何通过SSE协议与服务器通信
   - 提供完整的工具调用示例

4. **配置管理** (`config.py`)
   - 环境变量配置
   - 工具参数定义
   - 配置文件生成

## 🚀 快速开始

### 1. 环境准备

确保您的系统已安装Python 3.10或更高版本：

```bash
python --version
```

### 2. 安装依赖

```bash
# 使用uv安装依赖（推荐）
uv sync

# 或使用pip安装
pip install -r requirements.txt
```

### 3. 启动服务器

```bash
# 启动MCP服务器
python main.py
```

服务器将在 `http://localhost:7000` 启动，您会看到类似输出：

```
🚀 启动交互式MCP服务器...
📡 协议: MCP over SSE
🔧 工具: 用户输入捕获
INFO:     Started server process [12345]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:7000 (Press CTRL+C to quit)
```

### 4. 测试工具

在另一个终端中运行测试客户端：

```bash
python test_client.py
```

## 🔧 工具详细说明

### wait_for_user_input

**功能**: 暂停执行并等待用户输入

**参数**:
- `prompt` (string, 可选): 显示给用户的提示信息，默认为"请输入内容: "
- `timeout` (integer, 可选): 等待超时时间（秒），默认300秒，最大3600秒
- `input_type` (string, 可选): 期望的输入类型，支持：
  - `text`: 普通文本（默认）
  - `number`: 数字
  - `json`: JSON格式
  - `multiline`: 多行文本

**返回值**:
```json
{
  "user_input": "用户输入的内容",
  "original_input": "原始输入内容",
  "input_type": "输入类型",
  "prompt": "提示信息",
  "timestamp": 1234567890.123,
  "length": 15
}
```

**使用示例**:

```python
# 基本文本输入
response = await client.call_tool("wait_for_user_input", {
    "prompt": "请输入您的姓名: ",
    "timeout": 60,
    "input_type": "text"
})

# JSON输入
response = await client.call_tool("wait_for_user_input", {
    "prompt": "请输入配置JSON: ",
    "timeout": 120,
    "input_type": "json"
})

# 数字输入
response = await client.call_tool("wait_for_user_input", {
    "prompt": "请输入年龄: ",
    "timeout": 30,
    "input_type": "number"
})
```

### check_input_status

**功能**: 检查当前是否正在等待用户输入

**参数**: 无

**返回值**:
```json
{
  "is_waiting": false,
  "current_prompt": "",
  "timeout_seconds": 300
}
```

## 🌐 API端点

### HTTP端点

- `GET /health` - 健康检查
- `GET /info` - 服务器信息
- `POST /sse` - SSE连接端点（MCP通信）

### SSE事件

- `message` - MCP协议消息
- `error` - 错误信息

## ⚙️ 配置选项

### 环境变量

创建 `.env` 文件或设置环境变量：

```bash
# 服务器配置
MCP_HOST=0.0.0.0
MCP_PORT=7000
MCP_LOG_LEVEL=INFO

# 用户输入配置
MCP_INPUT_TIMEOUT=300
MCP_MAX_INPUT_TIMEOUT=3600

# CORS配置
MCP_CORS_ORIGINS=*

# 客户端配置
MCP_SERVER_URL=http://localhost:7000
MCP_REQUEST_TIMEOUT=30
```

### 生成配置文件

```bash
python config.py
```

## 🧪 测试场景

### 1. 基本功能测试

```bash
python test_client.py
# 选择 "1. 完整演示"
```

### 2. 超时测试

```bash
python test_client.py
# 选择 "2. 超时测试"
```

### 3. 手动测试

使用curl测试SSE连接：

```bash
curl -X POST http://localhost:7000/sse \
  -H "Content-Type: application/json" \
  -H "Accept: text/event-stream" \
  -d '{
    "jsonrpc": "2.0",
    "id": 1,
    "method": "tools/list",
    "params": {}
  }'
```

## 🔍 实现原理

### 1. MCP协议集成

工具严格遵循MCP协议规范：

- 使用标准的JSON-RPC 2.0消息格式
- 实现`tools/list`和`tools/call`方法
- 支持标准的错误处理机制

### 2. SSE协议实现

- 使用`sse-starlette`库实现SSE服务器
- 支持长连接和实时事件推送
- 自动处理连接断开和重连

### 3. 阻塞输入处理

```python
# 核心实现逻辑
def wait_for_input(self, prompt: str, timeout: int) -> str:
    # 1. 显示提示信息
    print(f"提示: {prompt}")

    # 2. 启动输入线程
    input_thread = threading.Thread(target=self._input_worker)
    input_thread.start()

    # 3. 等待输入或超时
    try:
        user_input = self.input_queue.get(timeout=timeout)
        return user_input
    except queue.Empty:
        raise TimeoutError("输入超时")
```

### 4. 线程安全设计

- 使用`queue.Queue`实现线程安全的消息传递
- 输入线程与主线程分离，避免阻塞服务器
- 正确处理线程生命周期和资源清理

## 🚨 注意事项

### 1. 部署环境

- **开发环境**: 直接运行，支持控制台输入
- **生产环境**: 需要考虑输入来源（Web界面、API等）
- **容器环境**: 确保TTY支持或使用替代输入方案

### 2. 安全考虑

- 输入验证和清理
- 超时控制防止资源耗尽
- CORS配置限制访问来源

### 3. 性能优化

- 合理设置超时时间
- 监控并发连接数
- 定期清理过期的输入会话

## 🔧 故障排除

### 常见问题

1. **服务器启动失败**
   ```bash
   # 检查端口占用
   lsof -i :7000

   # 更换端口
   MCP_PORT=8001 python main.py
   ```

2. **SSE连接失败**
   ```bash
   # 检查CORS配置
   curl -H "Origin: http://localhost:3000" http://localhost:7000/health
   ```

3. **输入超时**
   ```bash
   # 增加超时时间
   MCP_INPUT_TIMEOUT=600 python main.py
   ```

### 调试模式

启用详细日志：

```bash
MCP_LOG_LEVEL=DEBUG python main.py
```

## 📚 扩展开发

### 添加新工具

1. 在`InteractiveMCPServer.setup_tools()`中添加工具定义
2. 实现对应的处理函数
3. 更新工具列表和文档

### 自定义输入处理

继承`UserInputCapture`类并重写相关方法：

```python
class CustomInputCapture(UserInputCapture):
    def wait_for_input(self, prompt: str, timeout: int) -> str:
        # 自定义输入逻辑
        pass
```

## 📄 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 🤝 贡献

欢迎提交Issue和Pull Request！

---

**开发者**: Augment Agent
**版本**: 1.0.0
**更新时间**: 2025-06-16