#!/usr/bin/env python3
"""
基于MCP协议的交互式工具服务器
支持SSE协议，实现用户实时输入捕获功能
"""

import asyncio
import json
import logging
import queue
import threading
import time
from typing import Optional

import uvicorn
from mcp.server.fastmcp import FastMCP
from mcp.server.sse import SseServerTransport
from starlette.applications import Starlette
from starlette.middleware.cors import CORSMiddleware
from starlette.routing import Route, Mount


# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class UserInputCapture:
    """用户输入捕获类，处理阻塞式用户输入"""

    def __init__(self):
        self.input_queue = queue.Queue()
        self.is_waiting = False
        self.current_prompt = ""
        self.timeout_seconds = 300  # 默认5分钟超时

    def wait_for_input(self, prompt: str = "请输入内容: ",
                       timeout: Optional[int] = None) -> str:
        """
        等待用户输入

        Args:
            prompt: 提示信息
            timeout: 超时时间（秒），None表示使用默认超时

        Returns:
            用户输入的字符串

        Raises:
            TimeoutError: 超时异常
        """
        self.current_prompt = prompt
        self.is_waiting = True

        # 显示提示信息
        print(f"\n{'='*60}")
        print("🤖 AI工具正在等待您的输入...")
        print(f"📝 提示: {prompt}")
        print(f"⏰ 超时时间: {timeout or self.timeout_seconds}秒")
        print("💡 输入完成后请按回车键")
        print(f"{'='*60}")

        # 启动输入线程
        input_thread = threading.Thread(
            target=self._input_worker,
            daemon=True
        )
        input_thread.start()

        try:
            # 等待用户输入，带超时
            user_input = self.input_queue.get(
                timeout=timeout or self.timeout_seconds
            )
            self.is_waiting = False

            print(f"✅ 已接收到用户输入: {user_input[:50]}...")
            return user_input

        except queue.Empty:
            self.is_waiting = False
            error_msg = f"等待用户输入超时 ({timeout or self.timeout_seconds}秒)"
            print(f"❌ {error_msg}")
            raise TimeoutError(error_msg)

    def _input_worker(self):
        """输入工作线程"""
        try:
            user_input = input(f"{self.current_prompt}")
            if self.is_waiting:
                self.input_queue.put(user_input)
        except (EOFError, KeyboardInterrupt):
            if self.is_waiting:
                self.input_queue.put("__CANCELLED__")


# 全局用户输入捕获实例
user_input_capture = UserInputCapture()

# 创建FastMCP实例
mcp = FastMCP("interactive-mcp-server")

# 使用FastMCP装饰器定义工具


@mcp.tool()
async def wait_for_user_input(
    prompt: str = "请输入内容: ",
    timeout: int = 300,
    input_type: str = "text"
) -> str:
    """
    暂停执行并等待用户输入。工具会阻塞直到用户提供输入或超时。

    Args:
        prompt: 显示给用户的提示信息
        timeout: 等待超时时间（秒），默认300秒
        input_type: 期望的输入类型提示 (text, number, json, multiline)

    Returns:
        用户输入的内容
    """
    logger.info(f"开始等待用户输入，提示: {prompt}, 超时: {timeout}秒")

    try:
        # 在线程池中执行阻塞操作
        loop = asyncio.get_event_loop()
        user_input = await loop.run_in_executor(
            None,
            user_input_capture.wait_for_input,
            prompt,
            timeout
        )

        # 检查是否被取消
        if user_input == "__CANCELLED__":
            raise Exception("用户取消了输入操作")

        # 根据输入类型进行基本验证和格式化
        formatted_input = _format_input(user_input, input_type)

        result_content = {
            "user_input": formatted_input,
            "original_input": user_input,
            "input_type": input_type,
            "prompt": prompt,
            "timestamp": time.time(),
            "length": len(user_input)
        }

        logger.info(f"成功获取用户输入: {user_input[:100]}...")

        return f"用户输入已成功捕获:\n\n输入内容: {formatted_input}\n输入长度: {len(user_input)} 字符\n输入类型: {input_type}\n\n详细信息: {json.dumps(result_content, ensure_ascii=False, indent=2)}"

    except TimeoutError as e:
        logger.warning(f"用户输入超时: {e}")
        raise Exception(f"等待用户输入超时 ({timeout}秒)。请重新尝试或增加超时时间。")
    except Exception as e:
        logger.error(f"等待用户输入时发生错误: {e}")
        raise


@mcp.tool()
async def check_input_status() -> str:
    """检查当前是否正在等待用户输入"""
    status = {
        "is_waiting": user_input_capture.is_waiting,
        "current_prompt": user_input_capture.current_prompt,
        "timeout_seconds": user_input_capture.timeout_seconds
    }

    return f"输入状态: {json.dumps(status, ensure_ascii=False, indent=2)}"


def _format_input(user_input: str, input_type: str) -> str:
    """根据输入类型格式化用户输入"""
    if input_type == "number":
        try:
            # 尝试转换为数字
            if '.' in user_input:
                float(user_input)
            else:
                int(user_input)
            return user_input.strip()
        except ValueError:
            logger.warning(f"输入不是有效数字: {user_input}")
            return user_input.strip()

    elif input_type == "json":
        try:
            # 验证JSON格式
            json.loads(user_input)
            return user_input.strip()
        except json.JSONDecodeError:
            logger.warning(f"输入不是有效JSON: {user_input}")
            return user_input.strip()

    elif input_type == "multiline":
        # 保留换行符
        return user_input

    else:  # text
        return user_input.strip()


def create_sse_server(mcp_instance: FastMCP) -> Starlette:
    """创建处理SSE连接的Starlette应用"""
    transport = SseServerTransport("/messages/")

    async def handle_sse(request):
        """处理SSE连接"""
        async with transport.connect_sse(
            request.scope, request.receive, request._send
        ) as streams:
            await mcp_instance._mcp_server.run(
                streams[0], streams[1], mcp_instance._mcp_server.create_initialization_options(
                )
            )

    # 创建Starlette路由
    routes = [
        Route("/sse/", endpoint=handle_sse),
        Mount("/messages/", app=transport.handle_post_message),
    ]

    return Starlette(routes=routes)


def create_app():
    """创建FastAPI应用"""
    from fastapi import FastAPI

    # 创建FastAPI应用
    app = FastAPI(title="Interactive MCP Server", version="1.0.0")

    # 添加CORS中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # 健康检查端点
    @app.get("/health")
    async def health_check():
        return {
            "status": "healthy",
            "service": "interactive-mcp-server",
            "version": "1.0.0",
            "timestamp": time.time()
        }

    # 服务器信息端点
    @app.get("/info")
    async def server_info():
        return {
            "name": "Interactive MCP Server",
            "description": "基于MCP协议的交互式用户输入捕获工具",
            "version": "1.0.0",
            "protocol": "MCP over SSE",
            "tools": [
                {
                    "name": "wait_for_user_input",
                    "description": "等待用户输入"
                },
                {
                    "name": "check_input_status",
                    "description": "检查输入状态"
                }
            ]
        }

    # 挂载SSE服务器
    sse_app = create_sse_server(mcp)
    app.mount("/", sse_app)

    return app


def main():
    """主函数"""
    print("🚀 启动交互式MCP服务器...")
    print("📡 协议: MCP over SSE")
    print("🔧 工具: 用户输入捕获")

    app = create_app()

    # 启动服务器
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=7000,
        log_level="info",
        access_log=True
    )


if __name__ == "__main__":
    main()
