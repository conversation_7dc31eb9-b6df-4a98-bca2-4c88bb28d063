#!/usr/bin/env python3
"""
MCP交互式工具服务器启动脚本
提供更灵活的启动选项和配置
"""

import argparse
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config import MCPServerConfig, print_config_info
from main import create_app
import uvicorn


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="MCP交互式工具服务器",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python start_server.py                    # 使用默认配置启动
  python start_server.py --port 8001       # 指定端口
  python start_server.py --host 127.0.0.1  # 指定主机
  python start_server.py --debug           # 调试模式
  python start_server.py --config          # 显示配置信息
        """
    )
    
    parser.add_argument(
        "--host",
        default=MCPServerConfig.HOST,
        help=f"服务器主机地址 (默认: {MCPServerConfig.HOST})"
    )
    
    parser.add_argument(
        "--port",
        type=int,
        default=MCPServerConfig.PORT,
        help=f"服务器端口 (默认: {MCPServerConfig.PORT})"
    )
    
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default=MCPServerConfig.LOG_LEVEL,
        help=f"日志级别 (默认: {MCPServerConfig.LOG_LEVEL})"
    )
    
    parser.add_argument(
        "--debug",
        action="store_true",
        help="启用调试模式"
    )
    
    parser.add_argument(
        "--reload",
        action="store_true",
        help="启用自动重载（开发模式）"
    )
    
    parser.add_argument(
        "--config",
        action="store_true",
        help="显示当前配置信息并退出"
    )
    
    parser.add_argument(
        "--workers",
        type=int,
        default=1,
        help="工作进程数量 (默认: 1)"
    )
    
    return parser.parse_args()


def setup_environment(args):
    """设置环境变量"""
    if args.debug:
        os.environ["MCP_LOG_LEVEL"] = "DEBUG"
    else:
        os.environ["MCP_LOG_LEVEL"] = args.log_level
    
    os.environ["MCP_HOST"] = args.host
    os.environ["MCP_PORT"] = str(args.port)


def check_dependencies():
    """检查依赖是否安装"""
    required_packages = [
        "mcp",
        "uvicorn", 
        "starlette",
        "httpx",
        "httpx_sse"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ 缺少以下依赖包:")
        for package in missing_packages:
            print(f"  - {package}")
        print("\n请运行以下命令安装依赖:")
        print("  pip install -r requirements.txt")
        print("  或")
        print("  uv sync")
        sys.exit(1)


def main():
    """主函数"""
    args = parse_arguments()
    
    # 显示配置信息
    if args.config:
        print_config_info()
        return
    
    # 检查依赖
    print("🔍 检查依赖包...")
    check_dependencies()
    print("✅ 依赖检查通过")
    
    # 设置环境
    setup_environment(args)
    
    # 显示启动信息
    print("🚀 启动MCP交互式工具服务器")
    print("=" * 60)
    print(f"📡 协议: MCP over SSE")
    print(f"🌐 地址: http://{args.host}:{args.port}")
    print(f"📊 日志级别: {args.log_level}")
    print(f"🔧 调试模式: {'开启' if args.debug else '关闭'}")
    print(f"🔄 自动重载: {'开启' if args.reload else '关闭'}")
    print("=" * 60)
    
    print("\n可用端点:")
    print(f"  - 健康检查: http://{args.host}:{args.port}/health")
    print(f"  - 服务器信息: http://{args.host}:{args.port}/info")
    print(f"  - SSE连接: http://{args.host}:{args.port}/sse")
    
    print("\n🔧 可用工具:")
    print("  - wait_for_user_input: 等待用户输入")
    print("  - check_input_status: 检查输入状态")
    
    print(f"\n💡 测试客户端: python test_client.py")
    print("=" * 60)
    
    try:
        # 创建应用
        app = create_app()
        
        # 启动服务器
        uvicorn.run(
            app,
            host=args.host,
            port=args.port,
            log_level=args.log_level.lower(),
            reload=args.reload,
            workers=args.workers if not args.reload else 1,
            access_log=True
        )
        
    except KeyboardInterrupt:
        print("\n⚠️ 服务器已停止")
    except Exception as e:
        print(f"\n❌ 服务器启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
