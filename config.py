#!/usr/bin/env python3
"""
MCP交互式工具配置文件
"""

import os
from typing import Dict, Any


class MCPServerConfig:
    """MCP服务器配置"""
    
    # 服务器基本配置
    HOST = os.getenv("MCP_HOST", "0.0.0.0")
    PORT = int(os.getenv("MCP_PORT", "7000"))
    
    # 日志配置
    LOG_LEVEL = os.getenv("MCP_LOG_LEVEL", "INFO")
    
    # 用户输入配置
    DEFAULT_INPUT_TIMEOUT = int(os.getenv("MCP_INPUT_TIMEOUT", "300"))  # 5分钟
    MAX_INPUT_TIMEOUT = int(os.getenv("MCP_MAX_INPUT_TIMEOUT", "3600"))  # 1小时
    
    # CORS配置
    CORS_ORIGINS = os.getenv("MCP_CORS_ORIGINS", "*").split(",")
    
    # SSE配置
    SSE_ENDPOINT = os.getenv("MCP_SSE_ENDPOINT", "/sse")
    
    @classmethod
    def to_dict(cls) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "host": cls.HOST,
            "port": cls.PORT,
            "log_level": cls.LOG_LEVEL,
            "default_input_timeout": cls.DEFAULT_INPUT_TIMEOUT,
            "max_input_timeout": cls.MAX_INPUT_TIMEOUT,
            "cors_origins": cls.CORS_ORIGINS,
            "sse_endpoint": cls.SSE_ENDPOINT,
        }


class MCPClientConfig:
    """MCP客户端配置"""
    
    # 服务器连接配置
    SERVER_URL = os.getenv("MCP_SERVER_URL", "http://localhost:7000")
    
    # 请求超时配置
    REQUEST_TIMEOUT = int(os.getenv("MCP_REQUEST_TIMEOUT", "30"))
    
    # 重试配置
    MAX_RETRIES = int(os.getenv("MCP_MAX_RETRIES", "3"))
    RETRY_DELAY = float(os.getenv("MCP_RETRY_DELAY", "1.0"))
    
    @classmethod
    def to_dict(cls) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "server_url": cls.SERVER_URL,
            "request_timeout": cls.REQUEST_TIMEOUT,
            "max_retries": cls.MAX_RETRIES,
            "retry_delay": cls.RETRY_DELAY,
        }


# 工具配置
TOOL_CONFIGS = {
    "wait_for_user_input": {
        "name": "wait_for_user_input",
        "description": "暂停执行并等待用户输入。工具会阻塞直到用户提供输入或超时。",
        "default_timeout": MCPServerConfig.DEFAULT_INPUT_TIMEOUT,
        "max_timeout": MCPServerConfig.MAX_INPUT_TIMEOUT,
        "supported_input_types": ["text", "number", "json", "multiline"],
        "examples": [
            {
                "prompt": "请输入您的姓名: ",
                "input_type": "text",
                "timeout": 60
            },
            {
                "prompt": "请输入一个数字: ",
                "input_type": "number",
                "timeout": 30
            },
            {
                "prompt": "请输入JSON数据: ",
                "input_type": "json",
                "timeout": 120
            }
        ]
    },
    "check_input_status": {
        "name": "check_input_status",
        "description": "检查当前是否正在等待用户输入",
        "examples": [
            {
                "description": "检查输入状态",
                "arguments": {}
            }
        ]
    }
}


# 环境变量模板
ENV_TEMPLATE = """
# MCP服务器配置
MCP_HOST=0.0.0.0
MCP_PORT=7000
MCP_LOG_LEVEL=INFO

# 用户输入配置
MCP_INPUT_TIMEOUT=300
MCP_MAX_INPUT_TIMEOUT=3600

# CORS配置
MCP_CORS_ORIGINS=*

# SSE配置
MCP_SSE_ENDPOINT=/sse

# 客户端配置
MCP_SERVER_URL=http://localhost:7000
MCP_REQUEST_TIMEOUT=30
MCP_MAX_RETRIES=3
MCP_RETRY_DELAY=1.0
"""


def create_env_file(file_path: str = ".env"):
    """创建环境变量配置文件"""
    with open(file_path, "w", encoding="utf-8") as f:
        f.write(ENV_TEMPLATE.strip())
    print(f"✅ 环境变量配置文件已创建: {file_path}")


def print_config_info():
    """打印配置信息"""
    print("📋 当前配置信息:")
    print("\n🖥️  服务器配置:")
    server_config = MCPServerConfig.to_dict()
    for key, value in server_config.items():
        print(f"  {key}: {value}")
    
    print("\n📱 客户端配置:")
    client_config = MCPClientConfig.to_dict()
    for key, value in client_config.items():
        print(f"  {key}: {value}")
    
    print("\n🔧 工具配置:")
    for tool_name, tool_config in TOOL_CONFIGS.items():
        print(f"  {tool_name}: {tool_config['description']}")


if __name__ == "__main__":
    print("🔧 MCP交互式工具配置管理")
    print("=" * 50)
    
    print("\n请选择操作:")
    print("1. 查看当前配置")
    print("2. 创建环境变量文件")
    print("3. 退出")
    
    try:
        choice = input("\n请输入选择 (1-3): ").strip()
        
        if choice == "1":
            print_config_info()
        elif choice == "2":
            create_env_file()
        elif choice == "3":
            print("👋 再见!")
        else:
            print("❌ 无效选择")
    
    except KeyboardInterrupt:
        print("\n👋 再见!")
    except Exception as e:
        print(f"❌ 操作失败: {e}")
