#!/usr/bin/env python3
"""
简单测试脚本 - 验证MCP交互式工具的基本功能
"""

import asyncio
import json
import subprocess
import sys
import time
import signal
import os
from pathlib import Path

async def test_server_startup():
    """测试服务器启动"""
    print("🚀 测试服务器启动...")
    
    # 启动服务器进程
    process = subprocess.Popen(
        [sys.executable, "main.py"],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True
    )
    
    # 等待服务器启动
    await asyncio.sleep(3)
    
    # 检查进程是否还在运行
    if process.poll() is None:
        print("✅ 服务器启动成功")
        
        # 终止进程
        process.terminate()
        try:
            process.wait(timeout=5)
        except subprocess.TimeoutExpired:
            process.kill()
            process.wait()
        
        return True
    else:
        stdout, stderr = process.communicate()
        print(f"❌ 服务器启动失败")
        print(f"stdout: {stdout}")
        print(f"stderr: {stderr}")
        return False

async def test_health_endpoint():
    """测试健康检查端点"""
    print("🔍 测试健康检查端点...")
    
    try:
        import httpx
        
        # 启动服务器
        process = subprocess.Popen(
            [sys.executable, "main.py"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # 等待服务器启动
        await asyncio.sleep(3)
        
        # 测试健康检查
        async with httpx.AsyncClient() as client:
            response = await client.get("http://localhost:7000/health")
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 健康检查通过: {data['status']}")
                result = True
            else:
                print(f"❌ 健康检查失败: {response.status_code}")
                result = False
        
        # 终止服务器
        process.terminate()
        try:
            process.wait(timeout=5)
        except subprocess.TimeoutExpired:
            process.kill()
            process.wait()
        
        return result
        
    except ImportError:
        print("⚠️ httpx 未安装，跳过健康检查测试")
        return True
    except Exception as e:
        print(f"❌ 健康检查测试失败: {e}")
        return False

async def test_info_endpoint():
    """测试服务器信息端点"""
    print("📋 测试服务器信息端点...")
    
    try:
        import httpx
        
        # 启动服务器
        process = subprocess.Popen(
            [sys.executable, "main.py"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # 等待服务器启动
        await asyncio.sleep(3)
        
        # 测试信息端点
        async with httpx.AsyncClient() as client:
            response = await client.get("http://localhost:7000/info")
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 服务器信息获取成功")
                print(f"  名称: {data.get('name')}")
                print(f"  版本: {data.get('version')}")
                print(f"  工具数量: {len(data.get('tools', []))}")
                result = True
            else:
                print(f"❌ 服务器信息获取失败: {response.status_code}")
                result = False
        
        # 终止服务器
        process.terminate()
        try:
            process.wait(timeout=5)
        except subprocess.TimeoutExpired:
            process.kill()
            process.wait()
        
        return result
        
    except ImportError:
        print("⚠️ httpx 未安装，跳过信息端点测试")
        return True
    except Exception as e:
        print(f"❌ 信息端点测试失败: {e}")
        return False

def test_file_structure():
    """测试文件结构"""
    print("📁 测试文件结构...")
    
    required_files = [
        'main.py',
        'test_client.py', 
        'config.py',
        'start_server.py',
        'demo.py',
        'requirements.txt',
        'README.md',
        'Makefile'
    ]
    
    missing_files = []
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ 缺少文件: {missing_files}")
        return False
    
    print(f"✅ 文件结构完整 ({len(required_files)} 个文件)")
    return True

def test_imports():
    """测试基本导入"""
    print("📦 测试基本导入...")
    
    try:
        import main
        print("✅ main.py 导入成功")
        
        import config
        print("✅ config.py 导入成功")
        
        # 测试FastMCP实例
        if hasattr(main, 'mcp'):
            print("✅ FastMCP 实例创建成功")
        else:
            print("❌ FastMCP 实例未找到")
            return False
        
        # 测试用户输入捕获
        if hasattr(main, 'user_input_capture'):
            print("✅ 用户输入捕获实例创建成功")
        else:
            print("❌ 用户输入捕获实例未找到")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 导入测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🧪 MCP交互式工具 - 简单测试")
    print("=" * 50)
    
    tests = [
        ("文件结构检查", test_file_structure),
        ("基本导入测试", test_imports),
        ("服务器启动测试", test_server_startup),
        ("健康检查端点测试", test_health_endpoint),
        ("信息端点测试", test_info_endpoint),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}...")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            if result:
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            failed += 1
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 测试结果总结")
    print("=" * 50)
    print(f"✅ 通过: {passed}")
    print(f"❌ 失败: {failed}")
    print(f"📈 成功率: {passed/(passed+failed)*100:.1f}%")
    
    if failed == 0:
        print("\n🎉 所有测试通过！系统准备就绪。")
        print("\n下一步:")
        print("  1. 运行 'uv run python start_server.py' 启动服务器")
        print("  2. 在另一个终端运行 'uv run python test_client.py' 测试功能")
        print("  3. 运行 'uv run python demo.py' 查看完整演示")
        return True
    else:
        print(f"\n⚠️ 有 {failed} 个测试失败，请检查并修复问题。")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
